import 'package:flutter/material.dart';

/// IDE选择按钮组件
class IDEButton extends StatelessWidget {
  final String ideName;
  final String iconPath;
  final VoidCallback onPressed;
  final bool isSelected;

  const IDEButton({
    super.key,
    required this.ideName,
    required this.iconPath,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.transparent,
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey.shade400,
              width: isSelected ? 3 : 2,
            ),
            boxShadow: isSelected ? [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                spreadRadius: 2,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : [],
          ),
          child: Material(
            color: Colors.transparent,
            shape: const CircleBorder(),
            child: InkWell(
              customBorder: const CircleBorder(),
              onTap: onPressed,
              child: Center(
                child: _buildIcon(),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          ideName,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.blue.shade700 : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildIcon() {
    return SizedBox(
      width: 40,
      height: 40,
      child: Image.asset(
        iconPath,
        width: 40,
        height: 40,
        errorBuilder: (context, error, stackTrace) {
          return _getDefaultIcon();
        },
      ),
    );
  }

  Widget _getDefaultIcon() {
    IconData iconData;
    Color iconColor;
    
    switch (ideName.toLowerCase()) {
      case 'vs code':
        iconData = Icons.code;
        iconColor = Colors.blue;
        break;
      case 'intellij idea':
        iconData = Icons.lightbulb;
        iconColor = Colors.orange;
        break;
      case 'cursor':
        iconData = Icons.edit;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.computer;
        iconColor = Colors.grey;
    }
    
    return Icon(
      iconData,
      size: 40,
      color: iconColor,
    );
  }
}
