import 'package:flutter/material.dart';

/// IDE选择按钮组件
class IDEButton extends StatelessWidget {
  final String ideName;
  final String iconPath;
  final VoidCallback onPressed;
  final bool isSelected;

  const IDEButton({
    super.key,
    required this.ideName,
    required this.iconPath,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.shade300,
          width: isSelected ? 3 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: isSelected ? Colors.blue.shade50 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildIcon(),
                const SizedBox(width: 12),
                Text(
                  ideName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Colors.blue.shade700 : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return SizedBox(
      width: 32,
      height: 32,
      child: Image.asset(
        iconPath,
        width: 32,
        height: 32,
        errorBuilder: (context, error, stackTrace) {
          return _getDefaultIcon();
        },
      ),
    );
  }

  Widget _getDefaultIcon() {
    IconData iconData;
    Color iconColor;
    
    switch (ideName.toLowerCase()) {
      case 'vs code':
        iconData = Icons.code;
        iconColor = Colors.blue;
        break;
      case 'intellij idea':
        iconData = Icons.lightbulb;
        iconColor = Colors.orange;
        break;
      case 'cursor':
        iconData = Icons.edit;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.computer;
        iconColor = Colors.grey;
    }
    
    return Icon(
      iconData,
      size: 32,
      color: iconColor,
    );
  }
}
