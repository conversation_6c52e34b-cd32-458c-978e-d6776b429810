import 'package:flutter/material.dart';
import 'ide_button.dart';
import 'file_upload_modal.dart';
import 'user_info_card.dart';

/// IDE选择器组件
class IDESelector extends StatelessWidget {
  final String? selectedIDE;
  final Function(String) onIDESelected;

  const IDESelector({
    super.key,
    required this.selectedIDE,
    required this.onIDESelected,
  });

  void _showFileUploadModal(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return FileUploadModal(
          onClose: () => Navigator.of(context).pop(),
          onFilesSelected: (List<String> files) {
            print('Files selected: $files');
            // 这里可以处理选中的文件
          },
        );
      },
    );
  }

  void _showUserInfoModal(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            child: UserInfoCard(
              email: '<EMAIL>',
              remainingUsage: 750,
              totalUsage: 1000,
              expiryDate: DateTime.now().add(const Duration(days: 30)),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 圆形按钮水平排列
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // VS Code Button
            IDEButton(
              ideName: 'VS Code',
              iconPath: 'assets/icons/vscode.png',
              onPressed: () {
                onIDESelected('vscode');
                _showFileUploadModal(context);
              },
              isSelected: selectedIDE == 'vscode',
            ),

            // IntelliJ IDEA Button
            IDEButton(
              ideName: 'IntelliJ IDEA',
              iconPath: 'assets/icons/idea.png',
              onPressed: () {
                onIDESelected('intellij');
                _showFileUploadModal(context);
              },
              isSelected: selectedIDE == 'intellij',
            ),

            // Cursor Button
            IDEButton(
              ideName: 'Cursor',
              iconPath: 'assets/icons/cursor.png',
              onPressed: () {
                onIDESelected('cursor');
                _showFileUploadModal(context);
              },
              isSelected: selectedIDE == 'cursor',
            ),
          ],
        ),

        const SizedBox(height: 60),

        // 查看当前账号按钮
        TextButton.icon(
          onPressed: () => _showUserInfoModal(context),
          icon: Icon(
            Icons.account_circle_outlined,
            color: Theme.of(context).colorScheme.primary,
          ),
          label: Text(
            '查看当前账号',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          ),
        ),
      ],
    );
  }
}
