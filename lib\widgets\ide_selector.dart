import 'package:flutter/material.dart';
import 'ide_button.dart';

/// IDE选择器组件
class IDESelector extends StatelessWidget {
  final String? selectedIDE;
  final Function(String) onIDESelected;

  const IDESelector({
    super.key,
    required this.selectedIDE,
    required this.onIDESelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Select your IDE:',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 40),
        
        // VS Code Button
        IDEButton(
          ideName: 'VS Code',
          iconPath: 'assets/icons/vscode.png',
          onPressed: () => onIDESelected('vscode'),
          isSelected: selectedIDE == 'vscode',
        ),
        
        const SizedBox(height: 20),
        
        // IntelliJ IDEA Button
        IDEButton(
          ideName: 'IntelliJ IDEA',
          iconPath: 'assets/icons/idea.png',
          onPressed: () => onIDESelected('intellij'),
          isSelected: selectedIDE == 'intellij',
        ),
        
        const SizedBox(height: 20),
        
        // Cursor Button
        IDEButton(
          ideName: 'Cursor',
          iconPath: 'assets/icons/cursor.png',
          onPressed: () => onIDESelected('cursor'),
          isSelected: selectedIDE == 'cursor',
        ),
        
        const SizedBox(height: 40),
        
        if (selectedIDE != null)
          Text(
            'Selected: $selectedIDE',
            style: const TextStyle(fontSize: 18, color: Colors.green),
          ),
      ],
    );
  }
}
