import 'package:flutter/material.dart';
import 'ide_button.dart';
import 'file_upload_modal.dart';

/// IDE选择器组件
class IDESelector extends StatelessWidget {
  final String? selectedIDE;
  final Function(String) onIDESelected;

  const IDESelector({
    super.key,
    required this.selectedIDE,
    required this.onIDESelected,
  });

  void _showFileUploadModal(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return FileUploadModal(
          onClose: () => Navigator.of(context).pop(),
          onFilesSelected: (List<String> files) {
            print('Files selected: $files');
            // 这里可以处理选中的文件
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Select your IDE:',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 60),

        // 圆形按钮水平排列
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // VS Code Button
            IDEButton(
              ideName: 'VS Code',
              iconPath: 'assets/icons/vscode.png',
              onPressed: () {
                onIDESelected('vscode');
                _showFileUploadModal(context);
              },
              isSelected: selectedIDE == 'vscode',
            ),

            // IntelliJ IDEA Button
            IDEButton(
              ideName: 'IntelliJ IDEA',
              iconPath: 'assets/icons/idea.png',
              onPressed: () {
                onIDESelected('intellij');
                _showFileUploadModal(context);
              },
              isSelected: selectedIDE == 'intellij',
            ),

            // Cursor Button
            IDEButton(
              ideName: 'Cursor',
              iconPath: 'assets/icons/cursor.png',
              onPressed: () {
                onIDESelected('cursor');
                _showFileUploadModal(context);
              },
              isSelected: selectedIDE == 'cursor',
            ),
          ],
        ),

        const SizedBox(height: 60),

        if (selectedIDE != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Text(
              'Selected: $selectedIDE',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.green.shade700,
              ),
            ),
          ),
      ],
    );
  }
}
