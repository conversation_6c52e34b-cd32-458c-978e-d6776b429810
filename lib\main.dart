import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'IDE Selector',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'IDE Selector'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  String? selectedIDE;

  void _onIDESelected(String ide) {
    setState(() {
      selectedIDE = ide;
    });
    print('Selected IDE: $ide');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text(
              'Select your IDE:',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 40),

            // VS Code Button
            IDEButton(
              ideName: 'VS Code',
              iconPath: 'assets/icons/vscode.png',
              onPressed: () => _onIDESelected('vscode'),
              isSelected: selectedIDE == 'vscode',
            ),

            const SizedBox(height: 20),

            // IntelliJ IDEA Button
            IDEButton(
              ideName: 'IntelliJ IDEA',
              iconPath: 'assets/icons/idea.png',
              onPressed: () => _onIDESelected('intellij'),
              isSelected: selectedIDE == 'intellij',
            ),

            const SizedBox(height: 20),

            // Cursor Button
            IDEButton(
              ideName: 'Cursor',
              iconPath: 'assets/icons/cursor.png',
              onPressed: () => _onIDESelected('cursor'),
              isSelected: selectedIDE == 'cursor',
            ),

            const SizedBox(height: 40),

            if (selectedIDE != null)
              Text(
                'Selected: $selectedIDE',
                style: const TextStyle(fontSize: 18, color: Colors.green),
              ),
          ],
        ),
      ),
    );
  }
}

// IDE Button Component
class IDEButton extends StatelessWidget {
  final String ideName;
  final String iconPath;
  final VoidCallback onPressed;
  final bool isSelected;

  const IDEButton({
    super.key,
    required this.ideName,
    required this.iconPath,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.grey.shade300,
          width: isSelected ? 3 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: isSelected ? Colors.blue.shade50 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 使用 Image.asset 加载图标，如果图标不存在则显示默认图标
                _buildIcon(),
                const SizedBox(width: 12),
                Text(
                  ideName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Colors.blue.shade700 : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      width: 32,
      height: 32,
      child: Image.asset(
        iconPath,
        width: 32,
        height: 32,
        errorBuilder: (context, error, stackTrace) {
          // 如果图标加载失败，显示默认图标
          return _getDefaultIcon();
        },
      ),
    );
  }

  Widget _getDefaultIcon() {
    IconData iconData;
    Color iconColor;

    switch (ideName.toLowerCase()) {
      case 'vs code':
        iconData = Icons.code;
        iconColor = Colors.blue;
        break;
      case 'intellij idea':
        iconData = Icons.lightbulb;
        iconColor = Colors.orange;
        break;
      case 'cursor':
        iconData = Icons.edit;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.computer;
        iconColor = Colors.grey;
    }

    return Icon(
      iconData,
      size: 32,
      color: iconColor,
    );
  }
}
