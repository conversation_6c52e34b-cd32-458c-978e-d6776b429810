import 'package:flutter/material.dart';
import 'widgets/ide_selector.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'IDE Selector',
      debugShowCheckedModeBanner: false, // 隐藏 DEBUG 标签
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'IDE Selector'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  String? selectedIDE;

  void _onIDESelected(String ide) {
    setState(() {
      selectedIDE = ide;
    });
    print('Selected IDE: $ide');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: IDESelector(
          selectedIDE: selectedIDE,
          onIDESelected: _onIDESelected,
        ),
      ),
    );
  }
}
